
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import DealersTab from './DealersTab';
import APSTab from './APSTab';
import AreaOfficeTab from './AreaOfficeTab';

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState('dealers');

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Analytics Dashboard</h1>
          <p className="text-gray-600">Monitor performance across dealers, APS, and area offices</p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-8">
            <TabsTrigger 
              value="dealers" 
              className="text-lg py-3 data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              Dealers
            </TabsTrigger>
            <TabsTrigger 
              value="aps"
              className="text-lg py-3 data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              APS
            </TabsTrigger>
            <TabsTrigger 
              value="area-office"
              className="text-lg py-3 data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              Area Office
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dealers" className="space-y-6">
            <DealersTab />
          </TabsContent>

          <TabsContent value="aps" className="space-y-6">
            <APSTab />
          </TabsContent>

          <TabsContent value="area-office" className="space-y-6">
            <AreaOfficeTab />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;
